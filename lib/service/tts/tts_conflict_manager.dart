import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:dasso_reader/utils/error/common.dart';
import 'package:dasso_reader/utils/log/common.dart';

/// Manages TTS resource conflicts between Dictionary and Continuous Reading
/// 
/// This singleton service ensures that Dictionary pronunciation and Continuous Reading
/// TTS operations don't interfere with each other by coordinating resource access
/// and providing conflict resolution strategies.
class TtsConflictManager {
  static final TtsConflictManager _instance = TtsConflictManager._internal();
  
  factory TtsConflictManager() => _instance;
  
  TtsConflictManager._internal();

  /// Current active TTS context
  TtsContext? _activeContext;
  
  /// Current active engine type
  TtsEngineType? _activeEngine;
  
  /// Timestamp of when current operation started
  DateTime? _operationStartTime;
  
  /// Completer for current operation
  Completer<void>? _currentOperation;
  
  /// Lock for preventing concurrent access
  bool _isLocked = false;
  
  /// Notifier for context changes
  final ValueNotifier<TtsContext?> _contextNotifier = ValueNotifier(null);
  
  /// Get current active context
  TtsContext? get activeContext => _activeContext;
  
  /// Get current active engine
  TtsEngineType? get activeEngine => _activeEngine;
  
  /// Get context change notifier
  ValueNotifier<TtsContext?> get contextNotifier => _contextNotifier;
  
  /// Check if a specific context is currently active
  bool isContextActive(TtsContext context) => _activeContext == context;
  
  /// Check if any TTS operation is currently active
  bool get hasActiveOperation => _activeContext != null;
  
  /// Request exclusive access for a TTS operation
  /// 
  /// Returns true if access is granted immediately, false if blocked by another operation
  Future<bool> requestAccess(TtsContext context, TtsEngineType engine) async {
    AnxLog.info('TTS access requested for context: $context, engine: $engine');
    
    // If already locked by same context, allow
    if (_activeContext == context && _activeEngine == engine) {
      AnxLog.info('Access granted - same context and engine already active');
      return true;
    }
    
    // If locked by different context, handle conflict
    if (_isLocked && _activeContext != null && _activeContext != context) {
      return await _handleConflict(context, engine);
    }
    
    // Grant access
    await _grantAccess(context, engine);
    return true;
  }
  
  /// Release access for a TTS operation
  Future<void> releaseAccess(TtsContext context, TtsEngineType engine) async {
    if (_activeContext == context && _activeEngine == engine) {
      AnxLog.info('TTS access released for context: $context, engine: $engine');
      
      _activeContext = null;
      _activeEngine = null;
      _operationStartTime = null;
      _isLocked = false;
      
      // Complete current operation if exists
      if (_currentOperation != null && !_currentOperation!.isCompleted) {
        _currentOperation!.complete();
        _currentOperation = null;
      }
      
      _contextNotifier.value = null;
    }
  }
  
  /// Force release access (emergency stop)
  Future<void> forceRelease() async {
    AnxLog.warning('Force releasing TTS access');
    
    _activeContext = null;
    _activeEngine = null;
    _operationStartTime = null;
    _isLocked = false;
    
    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      _currentOperation!.complete();
      _currentOperation = null;
    }
    
    _contextNotifier.value = null;
  }
  
  /// Handle conflict between TTS contexts
  Future<bool> _handleConflict(TtsContext requestingContext, TtsEngineType requestingEngine) async {
    final currentContext = _activeContext!;
    final currentEngine = _activeEngine!;
    
    AnxLog.warning('TTS conflict detected: $currentContext ($currentEngine) vs $requestingContext ($requestingEngine)');
    
    // Dictionary pronunciation has priority over continuous reading
    if (requestingContext == TtsContext.dictionaryPronunciation && 
        currentContext == TtsContext.continuousReading) {
      
      AnxLog.info('Dictionary pronunciation taking priority over continuous reading');
      
      // Pause continuous reading
      await _pauseCurrentOperation();
      
      // Grant access to dictionary
      await _grantAccess(requestingContext, requestingEngine);
      return true;
    }
    
    // If continuous reading is requesting while dictionary is active, deny
    if (requestingContext == TtsContext.continuousReading && 
        currentContext == TtsContext.dictionaryPronunciation) {
      
      AnxLog.info('Denying continuous reading access - dictionary pronunciation active');
      
      // Create error for the requesting context
      final error = TtsError(
        context: requestingContext,
        failedEngine: requestingEngine,
        type: TtsErrorType.resourceEngineInUse,
        message: 'TTS engine is currently being used for dictionary pronunciation',
        severity: TtsErrorSeverity.low,
        technicalDetails: 'Active context: $currentContext, Active engine: $currentEngine',
      );
      
      error.log();
      return false;
    }
    
    // Same context but different engine - allow switch
    if (requestingContext == currentContext && requestingEngine != currentEngine) {
      AnxLog.info('Allowing engine switch within same context');
      await _grantAccess(requestingContext, requestingEngine);
      return true;
    }
    
    // Default: deny access
    AnxLog.warning('Access denied for $requestingContext ($requestingEngine)');
    return false;
  }
  
  /// Grant access to a TTS operation
  Future<void> _grantAccess(TtsContext context, TtsEngineType engine) async {
    _activeContext = context;
    _activeEngine = engine;
    _operationStartTime = DateTime.now();
    _isLocked = true;
    _currentOperation = Completer<void>();
    
    _contextNotifier.value = context;
    
    AnxLog.info('TTS access granted to context: $context, engine: $engine');
  }
  
  /// Pause current TTS operation
  Future<void> _pauseCurrentOperation() async {
    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      AnxLog.info('Pausing current TTS operation for context: $_activeContext');
      
      // Signal that current operation should pause
      _currentOperation!.complete();
      _currentOperation = null;
    }
  }
  
  /// Check if operation has timed out (for cleanup)
  bool get hasTimedOut {
    if (_operationStartTime == null) return false;
    
    const timeoutDuration = Duration(minutes: 5); // Reasonable timeout
    return DateTime.now().difference(_operationStartTime!) > timeoutDuration;
  }
  
  /// Cleanup timed out operations
  Future<void> cleanupTimedOut() async {
    if (hasTimedOut) {
      AnxLog.warning('Cleaning up timed out TTS operation for context: $_activeContext');
      await forceRelease();
    }
  }
  
  /// Get conflict resolution strategy for a given context
  ConflictResolutionStrategy getResolutionStrategy(TtsContext context) {
    switch (context) {
      case TtsContext.dictionaryPronunciation:
        return ConflictResolutionStrategy.interrupt;
      case TtsContext.continuousReading:
        return ConflictResolutionStrategy.queue;
    }
  }
  
  /// Dispose resources
  void dispose() {
    _contextNotifier.dispose();
    if (_currentOperation != null && !_currentOperation!.isCompleted) {
      _currentOperation!.complete();
    }
  }
}

/// Strategy for resolving TTS conflicts
enum ConflictResolutionStrategy {
  /// Interrupt current operation and take control
  interrupt,
  /// Wait in queue for current operation to complete
  queue,
  /// Deny access and fail gracefully
  deny,
}
